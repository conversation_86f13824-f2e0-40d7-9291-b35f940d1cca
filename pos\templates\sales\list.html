{% extends 'base/base.html' %}
{% load humanize %}
{% load static %}
{% block title %}Sales Management - POS System{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
<style>
    @media print {
        .no-print { display: none !important; }
        .print-only { display: block !important; }
        body { font-size: 12px; }
        .print-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
    }
    .print-only { display: none; }
    .sales-summary-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .action-button {
        transition: all 0.3s ease;
        transform: translateY(0);
    }
    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Professional table styling */
    .sales-table {
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #e5e7eb;
    }

    .sales-table thead th {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
        color: white;
        font-weight: 700;
        padding: 20px 24px;
        text-align: left;
        font-size: 13px;
        letter-spacing: 0.1em;
        text-transform: uppercase;
        border-bottom: 3px solid #1d4ed8;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .sales-table thead th:first-child {
        border-top-left-radius: 16px;
    }

    .sales-table thead th:last-child {
        border-top-right-radius: 16px;
    }

    .sales-table tbody td {
        padding: 18px 24px;
        border-bottom: 1px solid #f1f5f9;
        vertical-align: middle;
        font-size: 14px;
        color: #374151;
        background: white;
    }

    .sales-table tbody tr {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-left: 4px solid transparent;
    }

    .sales-table tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: translateX(4px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-left-color: #3b82f6;
    }

    .sales-table tbody tr:nth-child(even) {
        background-color: #fafbfc;
    }

    .sales-table tbody tr:nth-child(even):hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .sales-table tbody tr:last-child td:first-child {
        border-bottom-left-radius: 16px;
    }

    .sales-table tbody tr:last-child td:last-child {
        border-bottom-right-radius: 16px;
    }

    /* Enhanced status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 11px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 2px solid transparent;
    }

    .status-paid {
        background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
        color: white;
        border-color: #047857;
    }

    .status-pending {
        background: linear-gradient(135deg, #d97706 0%, #f59e0b 50%, #fbbf24 100%);
        color: white;
        border-color: #b45309;
    }

    /* Sale ID styling */
    .sale-id {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-weight: 700;
        color: #1e40af;
        background: #eff6ff;
        padding: 6px 12px;
        border-radius: 8px;
        border: 1px solid #dbeafe;
    }

    /* Amount styling */
    .amount-cell {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-weight: 600;
        color: #059669;
        font-size: 15px;
    }

    /* Date styling */
    .date-cell {
        color: #6b7280;
        font-size: 13px;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .sales-table thead th {
            padding: 0.75rem 0.5rem;
            font-size: 0.75rem;
        }

        .sales-table tbody td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
        }

        .action-button {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }
    }

    /* Loading state */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        background: white;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    /* Product display styling */
    .product-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.25rem;
        padding: 0.125rem 0;
    }

    .product-item:last-child {
        margin-bottom: 0;
    }

    .quantity-badge {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
        margin-right: 0.5rem;
        min-width: 2rem;
        text-align: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .product-name {
        font-size: 0.875rem;
        color: #374151;
        font-weight: 500;
        line-height: 1.2;
    }

    .products-column {
        max-width: 250px;
        min-width: 200px;
    }

    .products-scroll {
        max-height: 120px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #d1d5db #f3f4f6;
    }

    .products-scroll::-webkit-scrollbar {
        width: 4px;
    }

    .products-scroll::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 2px;
    }

    .products-scroll::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 2px;
    }

    .products-scroll::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }

    /* Enhanced action buttons */
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        padding: 8px 14px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }

    .action-btn-view {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        border-color: #4f46e5;
    }

    .action-btn-view:hover {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        box-shadow: 0 8px 15px rgba(99, 102, 241, 0.4);
    }

    .action-btn-print {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
        border-color: #047857;
    }

    .action-btn-print:hover {
        background: linear-gradient(135deg, #047857 0%, #059669 100%);
        box-shadow: 0 8px 15px rgba(16, 185, 129, 0.4);
    }

    .action-btn-email {
        background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
        color: white;
        border-color: #0284c7;
    }

    .action-btn-email:hover {
        background: linear-gradient(135deg, #0284c7 0%, #2563eb 100%);
        box-shadow: 0 8px 15px rgba(14, 165, 233, 0.4);
    }

    .action-btn-more {
        background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
        color: white;
        border-color: #4b5563;
        padding: 8px 12px;
    }

    .action-btn-more:hover {
        background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
        box-shadow: 0 8px 15px rgba(107, 114, 128, 0.4);
    }

    /* Table container styling */
    .table-container {
        background: white;
        border-radius: 20px;
        padding: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: 1px solid #e5e7eb;
    }
</style>
{% endblock %}

{% block content %}
    <!-- Print Header (only visible when printing) -->
    <div class="print-only print-header">
        <h1>Sales Report</h1>
        <p>Generated on: <span id="printDate"></span></p>
        <p>Total Records: {{ sales.paginator.count|default:sales|length }}</p>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <!-- Enhanced Header with Summary -->
        <div class="px-4 py-5 sm:px-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-chart-line text-indigo-600 mr-3"></i>
                        Sales Management
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">
                        Manage and track all sales transactions
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3">
                    <button onclick="printSalesReport()" class="action-button flex items-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-all duration-200">
                        <i class="fas fa-print mr-2"></i>
                        <span>Print Report</span>
                    </button>
                    <button onclick="exportToCSV()" class="action-button flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200">
                        <i class="fas fa-download mr-2"></i>
                        <span>Export CSV</span>
                    </button>
                    {% if sales %}
                        <button onclick="confirmDeleteAllSales()" class="action-button flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-all duration-200">
                            <i class="fas fa-trash-alt mr-2"></i>
                            <span>Delete All</span>
                        </button>
                    {% endif %}
                    <a href="/api/create/" class="action-button flex items-center bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        <span>New Sale</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Sales Summary Cards -->
        <div class="px-4 py-3 no-print">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg shadow-lg text-white">
                    <div class="flex items-center">
                        <i class="fas fa-money-bill-wave text-2xl mr-3"></i>
                        <div>
                            <p class="text-sm opacity-90">Total Sales</p>
                            <p class="text-xl font-bold" id="totalSalesAmount">₱0.00</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg shadow-lg text-white">
                    <div class="flex items-center">
                        <i class="fas fa-receipt text-2xl mr-3"></i>
                        <div>
                            <p class="text-sm opacity-90">Transactions</p>
                            <p class="text-xl font-bold">{{ sales.paginator.count|default:sales|length }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg shadow-lg text-white">
                    <div class="flex items-center">
                        <i class="fas fa-calendar-day text-2xl mr-3"></i>
                        <div>
                            <p class="text-sm opacity-90">Today's Sales</p>
                            <p class="text-xl font-bold" id="todaySalesAmount">₱0.00</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-lg shadow-lg text-white">
                    <div class="flex items-center">
                        <i class="fas fa-chart-bar text-2xl mr-3"></i>
                        <div>
                            <p class="text-sm opacity-90">Avg. Sale</p>
                            <p class="text-xl font-bold" id="avgSaleAmount">₱0.00</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Enhanced Search and Filter Section -->
        <div class="px-4 py-4 bg-gray-50 border-t border-gray-200 no-print">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Input -->
                <div class="flex-1">
                    <div class="relative">
                        <input
                            type="text"
                            name="search"
                            id="searchInput"
                            placeholder="Search by Sale ID, Date, Product Name, or Amount..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                        >
                        <div class="absolute left-3 top-2.5 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>

                <!-- Date Range Filter -->
                <div class="flex gap-2">
                    <input
                        type="date"
                        id="dateFrom"
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="From Date"
                    >
                    <input
                        type="date"
                        id="dateTo"
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="To Date"
                    >
                </div>

                <!-- Status Filter -->
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Status</option>
                    <option value="completed">Completed</option>
                    <option value="pending">Pending</option>
                    <option value="cancelled">Cancelled</option>
                </select>

                <!-- Action Buttons -->
                <div class="flex gap-2">
                    <button
                        type="button"
                        onclick="applyFilters()"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200"
                    >
                        <i class="fas fa-filter mr-2"></i>
                        Filter
                    </button>
                    <button
                        type="button"
                        onclick="clearFilters()"
                        class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200"
                    >
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <div class="table-container">
            <div class="overflow-x-auto max-h-[70vh] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <table class="min-w-full sales-table">
                <thead class="sticky top-0 z-10">
                    <tr>
                        <th class="text-left">
                            <i class="fas fa-receipt mr-2"></i>Sale ID
                        </th>
                        <th class="text-left">
                            <i class="fas fa-calendar-alt mr-2"></i>Date
                        </th>
                        <th class="text-left">
                            <i class="fas fa-box mr-2"></i>Products
                        </th>
                        <th class="text-left">
                            <i class="fas fa-calculator mr-2"></i>Subtotal
                        </th>
                        <th class="text-left">
                            <i class="fas fa-percentage mr-2"></i>Tax
                        </th>
                        <th class="text-left">
                            <i class="fas fa-money-bill-wave mr-2"></i>Total
                        </th>
                        <th class="text-left">
                            <i class="fas fa-check-circle mr-2"></i>Status
                        </th>
                        <th class="text-left">
                            <i class="fas fa-cogs mr-2"></i>Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white">
                    {% for sale in sales %}
                        <tr>
                            <td class="whitespace-nowrap">
                                <span class="sale-id">#{{ sale.id }}</span>
                            </td>
                            <td class="whitespace-nowrap date-cell">
                                <i class="far fa-calendar-alt mr-2"></i>{{ sale.created_at|date:"M j, Y" }}<br>
                                <small class="text-gray-400">{{ sale.created_at|date:"H:i" }}</small>
                            </td>
                            <td class="products-column">
                                <div class="products-scroll">
                                    {% for item in sale.items.all %}
                                        <div class="product-item">
                                            <span class="quantity-badge">
                                                {{ item.quantity }}x
                                            </span>
                                            <span class="product-name">{{ item.product.name }}</span>
                                        </div>
                                    {% empty %}
                                        <span class="text-gray-400 italic">No items</span>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="whitespace-nowrap amount-cell">
                                ₱{{ sale.subtotal|intcomma }}
                            </td>
                            <td class="whitespace-nowrap amount-cell">
                                ₱{{ sale.tax_amount|intcomma }}
                            </td>
                            <td class="whitespace-nowrap amount-cell">
                                <strong>₱{{ sale.total_amount|intcomma }}</strong>
                            </td>
                            <td class="whitespace-nowrap">
                                {% if sale.payment_status %}
                                    <span class="status-badge status-paid">
                                        <i class="fas fa-check-circle mr-1"></i>Paid
                                    </span>
                                {% else %}
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </td>
                            <td class="whitespace-nowrap">
                                <div class="action-buttons">
                                    <a href="{% url 'sale_details' sale.id %}"
                                       class="action-btn action-btn-view"
                                       title="View Details">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </a>
                                    <button onclick="reprintReceipt({{ sale.id }})"
                                            class="action-btn action-btn-print"
                                            title="Print Receipt">
                                        <i class="fas fa-print mr-1"></i>Print
                                    </button>
                                    <button onclick="emailReceipt({{ sale.id }})"
                                            class="action-btn action-btn-email"
                                            title="Email Receipt">
                                        <i class="fas fa-envelope mr-1"></i>Email
                                    </button>
                                    <div class="relative">
                                        <button onclick="toggleDropdown({{ sale.id }})"
                                                class="action-btn action-btn-more"
                                                title="More Actions">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div id="dropdown-{{ sale.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 border">
                                            <div class="py-1">
                                                <button onclick="duplicateSale({{ sale.id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-copy mr-2"></i>Duplicate Sale
                                                </button>
                                                <button onclick="refundSale({{ sale.id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-undo mr-2"></i>Process Refund
                                                </button>
                                                <button onclick="exportSalePDF({{ sale.id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-file-pdf mr-2"></i>Export PDF
                                                </button>
                                                <hr class="my-1">
                                                <button onclick="deleteSale({{ sale.id }})" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                    <i class="fas fa-trash mr-2"></i>Delete Sale
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            </div>
        </div>

        <div class="px-4 py-3 bg-gray-50 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if sales.has_previous %}
                    <a href="?page={{ sales.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                {% endif %}
                {% if sales.has_next %}
                    <a href="?page={{ sales.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div class="flex items-center justify-between w-full">
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ sales.start_index }}</span>
                        to
                        <span class="font-medium">{{ sales.end_index }}</span>
                        of
                        <span class="font-medium">{{ sales.paginator.count }}</span>
                        results
                    </p>

                </div>
                <div>
                    <nav class="relative z-0 inline-flex items-center space-x-4" aria-label="Pagination">
                        <div class="flex items-center space-x-1 bg-white rounded-lg shadow-sm">
                            {% if sales.has_previous %}
                                <a href="?page={{ sales.previous_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:text-indigo-600 transition-colors duration-200">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% endif %}

                            {% for i in sales.paginator.page_range %}
                                {% if sales.number == i %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600 shadow-sm">{{ i }}</span>
                                {% else %}
                                    <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200">{{ i }}</a>
                                {% endif %}
                            {% endfor %}

                            {% if sales.has_next %}
                                <a href="?page={{ sales.next_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:text-indigo-600 transition-colors duration-200">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% endif %}
                        </div>
                        {% comment %} <button onclick="bulkDelete()" class="flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-all duration-200 transform hover:scale-105 shadow-md">
                            <i class="fas fa-trash-alt mr-2"></i>
                            <span>Delete Selected</span>
                        </button> {% endcomment %}
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            calculateSummaryStats();
            setupEventListeners();
            setDefaultDates();
        });

        // Number formatting function
        function formatNumber(number) {
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // Calculate and display summary statistics
        function calculateSummaryStats() {
            const rows = document.querySelectorAll('tbody tr');
            let totalSales = 0;
            let todaySales = 0;
            const today = new Date().toDateString();

            rows.forEach(row => {
                const amountCell = row.querySelector('td:nth-child(4)');
                const dateCell = row.querySelector('td:nth-child(2)');

                if (amountCell && dateCell) {
                    const amount = parseFloat(amountCell.textContent.replace('₱', '').replace(',', '')) || 0;
                    totalSales += amount;

                    const saleDate = new Date(dateCell.textContent.trim()).toDateString();
                    if (saleDate === today) {
                        todaySales += amount;
                    }
                }
            });

            const avgSale = rows.length > 0 ? totalSales / rows.length : 0;

            document.getElementById('totalSalesAmount').textContent = `₱${totalSales.toLocaleString('en-PH', {minimumFractionDigits: 2})}`;
            document.getElementById('todaySalesAmount').textContent = `₱${todaySales.toLocaleString('en-PH', {minimumFractionDigits: 2})}`;
            document.getElementById('avgSaleAmount').textContent = `₱${avgSale.toLocaleString('en-PH', {minimumFractionDigits: 2})}`;
        }

        // Setup event listeners
        function setupEventListeners() {
            // Search input with debounce
            let searchTimeout;
            document.getElementById('searchInput').addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    filterTable();
                }, 300);
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.relative')) {
                    document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
                        dropdown.classList.add('hidden');
                    });
                }
            });
        }

        // Set default dates (last 30 days)
        function setDefaultDates() {
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
        }

        // Enhanced printing functionality
        function printSalesReport() {
            // Set print date
            document.getElementById('printDate').textContent = new Date().toLocaleString();

            // Print the page
            window.print();
        }

        // Export to CSV functionality
        function exportToCSV() {
            const rows = document.querySelectorAll('table tbody tr');
            let csvContent = 'Sale ID,Date,Products,Subtotal,Tax,Total Amount,Status\n';

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 7) {
                    const saleId = cells[0].textContent.trim().replace('#', '');
                    const date = cells[1].textContent.trim();
                    const products = cells[2].textContent.trim().replace(/\n/g, ' | ').replace(/\s+/g, ' ');
                    const subtotal = cells[3].textContent.trim().replace('₱', '').replace(',', '');
                    const tax = cells[4].textContent.trim().replace('₱', '').replace(',', '');
                    const total = cells[5].textContent.trim().replace('₱', '').replace(',', '');
                    const status = cells[6].textContent.trim();

                    csvContent += `"${saleId}","${date}","${products}","${subtotal}","${tax}","${total}","${status}"\n`;
                }
            });

            // Create and download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `sales_report_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Filter functionality
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const status = document.getElementById('statusFilter').value.toLowerCase();

            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const saleId = cells[0].textContent.toLowerCase();
                const date = cells[1].textContent;
                const products = cells[2].textContent.toLowerCase();
                const subtotal = cells[3].textContent.toLowerCase();
                const tax = cells[4].textContent.toLowerCase();
                const total = cells[5].textContent.toLowerCase();
                const saleStatus = cells[6].textContent.toLowerCase();

                let showRow = true;

                // Search filter - now includes product names
                if (searchTerm && !saleId.includes(searchTerm) && !products.includes(searchTerm) &&
                    !subtotal.includes(searchTerm) && !tax.includes(searchTerm) && !total.includes(searchTerm)) {
                    showRow = false;
                }

                // Date range filter
                if (dateFrom || dateTo) {
                    const saleDate = new Date(date);
                    if (dateFrom && saleDate < new Date(dateFrom)) showRow = false;
                    if (dateTo && saleDate > new Date(dateTo)) showRow = false;
                }

                // Status filter
                if (status && !saleStatus.includes(status)) {
                    showRow = false;
                }

                row.style.display = showRow ? '' : 'none';
            });
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('statusFilter').value = '';

            // Show all rows
            document.querySelectorAll('tbody tr').forEach(row => {
                row.style.display = '';
            });
        }

        function filterTable() {
            applyFilters();
        }

        // Dropdown toggle
        function toggleDropdown(saleId) {
            const dropdown = document.getElementById(`dropdown-${saleId}`);
            const isHidden = dropdown.classList.contains('hidden');

            // Close all dropdowns first
            document.querySelectorAll('[id^="dropdown-"]').forEach(d => d.classList.add('hidden'));

            // Toggle current dropdown
            if (isHidden) {
                dropdown.classList.remove('hidden');
            }
        }

        // Enhanced receipt printing
        function reprintReceipt(saleId) {
            // Show loading state
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Printing...';
            button.disabled = true;

            // Simulate API call to get receipt data
            fetch(`/api/receipt/${saleId}/`)
                .then(response => response.json())
                .then(data => {
                    // Create receipt window
                    const receiptWindow = window.open('', '_blank', 'width=300,height=600');
                    receiptWindow.document.write(generateReceiptHTML(data));
                    receiptWindow.document.close();
                    receiptWindow.print();
                    receiptWindow.close();
                })
                .catch(error => {
                    console.error('Error printing receipt:', error);
                    alert('Error printing receipt. Please try again.');
                })
                .finally(() => {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }

        // Generate receipt HTML using the same format as create.html
        function generateReceiptHTML(data) {
            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Receipt #${data.id}</title>
                    <style>
                        body {
                            font-family: 'Courier New', monospace;
                            width: 57mm;
                            margin: 0 auto;
                            padding: 5mm;
                            background-color: #fff;
                        }
                    </style>
                </head>
                <body>
                    <div style="font-family: 'Courier New', monospace; width: 57mm; margin: 0 auto; padding: 5mm; background-color: #fff;">
                        <div style="text-align: left; margin-bottom: 3mm; border-bottom: 1px solid #000; padding-bottom: 2mm;">
                            <h2 style="font-size: 12pt; margin-bottom: 2mm; text-align: center;">REFERENCE RECEIPT</h2>
                            <p style="font-size: 8pt; margin: 1mm 0;">Date: ${new Date(data.created_at).toLocaleDateString()}</p>
                            <p style="font-size: 8pt; margin: 1mm 0;">Time: ${new Date(data.created_at).toLocaleTimeString()}</p>
                            <p style="font-size: 8pt; margin: 1mm 0;">Receipt #: ${data.id}</p>
                            <p style="font-size: 8pt; margin: 1mm 0;">Ref #: ${data.payment_reference}</p>
                        </div>

                        <div style="margin-bottom: 2mm; border-bottom: 1px dashed #000; padding-bottom: 2mm; font-size: 8pt; text-transform: uppercase;">
                            ${data.items.map(item => `
                                <div style="display: flex; justify-content: space-between; margin: 1mm 0;">
                                    <span>${item.product_name} × ${item.quantity}</span>
                                    <span>₱${formatNumber((item.quantity * item.price_at_sale).toFixed(2))}</span>
                                </div>
                            `).join('')}
                        </div>

                        <div style="border-top: 1px solid #000; padding-top: 2mm; margin-top: 2mm;">
                            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                                <span>Subtotal:</span>
                                <span>₱${formatNumber(parseFloat(data.subtotal).toFixed(2))}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                                <span>Tax (12%):</span>
                                <span>₱${formatNumber(parseFloat(data.tax_amount).toFixed(2))}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 2mm; padding-top: 2mm; border-top: 1px solid #000; font-size: 10pt;">
                                <span>Total:</span>
                                <span>₱${formatNumber(parseFloat(data.total_amount).toFixed(2))}</span>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 3mm; padding-top: 2mm; border-top: 1px solid #000;">
                            <p style="font-size: 8pt;">Thank you for your purchase!</p>

                            <!-- Reference Only Disclaimer -->
                            <div style="margin-top: 3mm; padding-top: 2mm; border-top: 2px solid #000; font-size: 8pt; text-align: center; background-color: #f0f0f0;">
                                <p style="margin: 1mm 0; font-weight: bold; font-size: 9pt;">*** FOR REFERENCE ONLY ***</p>
                            </div>

                            <!-- BIR Compliance Template (For Future Use) -->
                            <div style="margin-top: 2mm; padding-top: 2mm; border-top: 1px dashed #000; font-size: 6pt; text-align: center; color: #888;">
                                <p style="margin: 0.5mm 0;">When registered with BIR, update:</p>
                                <p style="margin: 0.5mm 0;">TIN: [Your TIN Number]</p>
                                <p style="margin: 0.5mm 0;">Permit No: [ATP Number]</p>
                                <p style="margin: 0.5mm 0;">Date Issued: [ATP Date]</p>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;
        }

        // Additional action functions
        function emailReceipt(saleId) {
            const email = prompt('Enter email address:');
            if (email) {
                // Implement email functionality
                alert(`Receipt for Sale #${saleId} will be sent to ${email}`);
            }
        }

        function duplicateSale(saleId) {
            if (confirm('Create a new sale with the same items?')) {
                window.location.href = `/api/create/?duplicate=${saleId}`;
            }
        }

        function refundSale(saleId) {
            if (confirm('Process a refund for this sale?')) {
                // Implement refund functionality
                alert(`Refund process initiated for Sale #${saleId}`);
            }
        }

        function exportSalePDF(saleId) {
            window.open(`/sales/${saleId}/pdf/`, '_blank');
        }

        function deleteSale(saleId) {
            if (confirm('Are you sure you want to delete this sale?')) {
                fetch(`/sales/delete/${saleId}/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error deleting sale');
                    }
                });
            }
        }

        // Delete All Sales Function
        function confirmDeleteAllSales() {
            // Get sales count from the page
            const salesRows = document.querySelectorAll('tbody tr');
            const salesCount = salesRows.length;

            if (salesCount === 0) {
                alert('No sales to delete.');
                return;
            }

            const confirmMessage = `🚨 EXTREME WARNING: This will permanently delete ALL ${salesCount} sales records!\n\n` +
                                 `This action will:\n` +
                                 `• Remove all sales data and transaction history\n` +
                                 `• Delete all associated sale items\n` +
                                 `• Permanently erase financial records\n` +
                                 `• Cannot be undone or recovered\n\n` +
                                 `⚠️ This is a DESTRUCTIVE operation that will affect your business records!\n\n` +
                                 `Are you absolutely certain you want to continue?`;

            if (confirm(confirmMessage)) {
                // Triple confirmation for sales data (most critical)
                const secondConfirm = confirm(`🔥 CRITICAL CONFIRMATION\n\nYou are about to delete ${salesCount} sales records.\n\nThis will permanently remove all transaction history and cannot be recovered.\n\nDo you really want to proceed?`);

                if (secondConfirm) {
                    const finalConfirm = confirm(`⚠️ FINAL WARNING ⚠️\n\nLast chance to cancel!\n\nDeleting all sales data will:\n• Remove financial history\n• Affect reporting and analytics\n• Cannot be undone\n\nType 'DELETE' in the next prompt to confirm.`);

                    if (finalConfirm) {
                        const typeConfirm = prompt('Type "DELETE" to confirm deletion of all sales:');

                        if (typeConfirm === 'DELETE') {
                            // Show loading state
                            const deleteBtn = event.target;
                            const originalText = deleteBtn.innerHTML;
                            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';
                            deleteBtn.disabled = true;

                            // Create and submit form
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = '{% url "delete_all_sales" %}';

                            const csrfToken = document.createElement('input');
                            csrfToken.type = 'hidden';
                            csrfToken.name = 'csrfmiddlewaretoken';
                            csrfToken.value = '{{ csrf_token }}';
                            form.appendChild(csrfToken);

                            document.body.appendChild(form);
                            form.submit();
                        } else {
                            alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
                        }
                    }
                }
            }
        }
    </script>

    <script src="{% static 'js/receipt.js' %}"></script>
{% endblock %}


