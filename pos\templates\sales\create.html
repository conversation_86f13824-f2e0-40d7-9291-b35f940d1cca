{% extends 'base/base.html' %}
{% load static %}

{% block title %}Point of Sale - POS System{% endblock %}

{% block extra_css %}
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    body {
        font-family: 'Poppins', sans-serif;
    }

    /* Override base template container width for POS page */
    .max-w-7xl {
        max-width: none !important;
    }

    main .py-6 > div {
        max-width: none !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .card-hover {
        transition: all 0.3s ease;
    }
    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    /* Add to existing styles */
    #productGrid > div {
        transition: all 0.3s ease-in-out;
    }

    .search-highlight {
        background-color: rgba(99, 102, 241, 0.1);
        transform: scale(1.05);
    }

    #productSearch, #barcodeInput {
        transition: all 0.2s ease;
    }

    #productSearch:focus, #barcodeInput:focus {
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }
    .hero-gradient {
        background: linear-gradient(135deg, #EBF4FF 0%, #C3DAFE 100%);
    }

    /* Make product cards more compact for wider layout */
    .product-card {
        min-height: 200px;
    }

    /* Keyboard shortcuts styling */
    kbd {
        font-family: 'Courier New', monospace;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 2px 6px;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        border: 1px solid #dee2e6;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        color: #495057;
        display: inline-block;
        min-width: 20px;
        text-align: center;
    }

    .shortcut-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        font-size: 0.6rem;
        font-weight: bold;
        padding: 1px 4px;
        border-radius: 3px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        z-index: 10;
    }

    .keyboard-hint {
        animation: subtle-pulse 3s infinite;
    }

    @keyframes subtle-pulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
    }

    /* Product card keyboard navigation styling */
    .product-card.keyboard-focused {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        transform: scale(1.02);
    }

    /* Quick shortcuts panel styling */
    .shortcuts-panel {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        border: 1px solid #e2e8f0;
    }

    .shortcuts-panel:hover {
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full px-2">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-lg mb-6 p-6">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cash-register text-3xl text-indigo-600"></i>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Point of Sale System</h1>
                    <p class="text-gray-600">Create New Sale - Cash Register</p>
                </div>
            </div>
            <div class="hidden sm:flex items-center space-x-3 text-gray-600 bg-gray-50 px-4 py-3 rounded-lg">
                <i class="far fa-calendar-alt text-lg"></i>
                <div class="text-right">
                    <p class="text-sm">{% now "F j, Y" %}</p>
                    <p class="text-lg font-semibold"><i class="far fa-clock mr-1"></i>{% now "H:i" %}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                <!-- Main Content -->
                <div class="xl:col-span-3 space-y-6">
                    <!-- Barcode Scanner -->
                    <div class="bg-white rounded-lg shadow p-6 transform hover:scale-[1.02] transition-transform duration-300">
                        <div class="relative">
                            <i class="fas fa-barcode absolute left-4 top-4 text-gray-400 text-xl"></i>
                            <input type="text" id="barcodeInput"
                                   class="w-full pl-12 pr-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
                                   placeholder="Scan Barcode"
                                   autofocus>
                        </div>
                    </div>

                    <!-- Product Search -->
                    <div class="bg-white rounded-lg shadow p-6 transform hover:scale-[1.02] transition-transform duration-300">
                        <div class="relative mb-4">
                            <i class="fas fa-search absolute left-4 top-4 text-gray-400 text-xl"></i>
                            <input type="text" id="productSearch"
                                   class="w-full pl-12 pr-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
                                   placeholder="Search products...">
                        </div>

                        <!-- Advanced Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <select id="categoryFilter" class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Categories</option>
                            </select>
                            <input type="number" id="minPrice" placeholder="Min Price"
                                   class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            <input type="number" id="maxPrice" placeholder="Max Price"
                                   class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                        </div>

                        <div id="productGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 max-h-[60vh] overflow-y-auto p-4">
                            {% for product in products %}
                                <div class="card-hover product-card bg-white rounded-xl p-4 border-2 border-gray-100"
                                     data-id="{{ product.id }}"
                                     data-barcode="{{ product.barcode }}"
                                     data-category="{{ product.category.name }}"
                                     data-price="{{ product.price }}">
                                    <div class="flex items-center justify-center mb-2">
                                        <div class="bg-indigo-100 p-2 rounded-lg">
                                            <i class="fas fa-box text-indigo-600"></i>
                                        </div>
                                    </div>
                                    <h3 class="font-bold text-gray-800 text-sm text-center mb-2">{{ product.name|title }}</h3>
                                    <p class="text-indigo-600 font-bold text-lg text-center flex items-center justify-center">
                                        <i class="fas fa-tag text-indigo-400 mr-1"></i>
                                        ₱{{ product.price }}
                                    </p>
                                    <div class="mt-2 text-center">
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full {% if product.stock_quantity <= 10 %}bg-red-100 text-red-800 border border-red-200{% else %}bg-green-100 text-green-800 border border-green-200{% endif %}">
                                            <i class="fas fa-cubes mr-1"></i>
                                            {{ product.stock_quantity }}
                                        </span>
                                    </div>
                                    <button onclick="addProduct('{{ product.id }}', '{{ product.name }}', '{{ product.price }}', '{{ product.stock_quantity }}')"
                                            class="w-full mt-3 bg-indigo-600 text-white py-2 rounded-lg font-semibold hover:bg-indigo-700 transition-colors flex items-center justify-center text-sm
                                                   {% if product.stock_quantity <= 0 %}opacity-50 cursor-not-allowed{% endif %}"
                                            {% if product.stock_quantity <= 0 %}disabled{% endif %}>
                                        <i class="fas fa-cart-plus mr-1"></i>
                                        {% if product.stock_quantity <= 0 %}
                                            Out of Stock
                                        {% else %}
                                            Add
                                        {% endif %}
                                    </button>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Load More Button -->
                        <div class="text-center mt-4">
                            <button id="loadMoreBtn" onclick="loadMoreProducts()"
                                    class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors hidden">
                                <i class="fas fa-plus mr-2"></i>
                                Load More Products
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Cart -->
                <div class="xl:col-span-1">
                    <div class="bg-white rounded-lg shadow p-6 sticky top-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold flex items-center">
                                <i class="fas fa-shopping-cart text-indigo-600 mr-2"></i>
                                Shopping Cart
                            </h2>
                            <button onclick="resetCart()"
                                    class="px-3 py-1 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors flex items-center text-sm font-medium"
                                    title="Clear all items from cart">
                                <i class="fas fa-trash-alt mr-1"></i>
                                Reset
                            </button>
                        </div>
                        <div id="cartItems" class="space-y-4 max-h-[60vh] overflow-y-auto mb-6 border rounded-lg p-4 bg-gray-50">
                            <!-- Cart items here -->
                        </div>

                        <div class="border-t pt-4 space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="flex items-center">
                                    <i class="fas fa-receipt text-gray-500 mr-2"></i>
                                    Subtotal
                                </span>
                                <span id="subtotal" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="flex items-center">
                                    <i class="fas fa-percentage text-gray-500 mr-2"></i>
                                    Tax (12%)
                                </span>
                                <span id="tax" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-xl font-bold text-indigo-600">
                                <span class="flex items-center">
                                    <i class="fas fa-coins mr-2"></i>
                                    Total
                                </span>
                                <span id="total">₱0.00</span>
                            </div>
                        </div>

                        <button onclick="showPaymentModal()"
                                class="w-full mt-6 bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-credit-card mr-2"></i>
                            Proceed to Checkout
                            <span class="ml-2 text-xs bg-indigo-500 px-2 py-1 rounded">F9</span>
                        </button>

                        <!-- Keyboard Shortcuts Quick Reference -->
                        <div class="mt-4 p-4 shortcuts-panel rounded-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-semibold text-gray-700 flex items-center">
                                    <i class="fas fa-keyboard mr-2 text-indigo-600 keyboard-hint"></i>
                                    Quick Shortcuts
                                </h3>
                                <button onclick="window.posKeyboardShortcuts?.showHelpModal()"
                                        class="text-xs text-indigo-600 hover:text-indigo-800 font-medium transition-colors">
                                    View All <kbd>Ctrl+H</kbd>
                                </button>
                            </div>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Search:</span>
                                    <kbd>Ctrl+F</kbd>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Barcode:</span>
                                    <kbd>Ctrl+B</kbd>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Reset Cart:</span>
                                    <kbd>Ctrl+R</kbd>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Quick Add:</span>
                                    <kbd>F1-F12</kbd>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Navigate:</span>
                                    <kbd>Tab</kbd>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">New Sale:</span>
                                    <kbd>Ctrl+N</kbd>
                                </div>
                            </div>
                            <div class="mt-3 pt-3 border-t border-gray-200">
                                <div class="flex items-center justify-center text-xs text-gray-500">
                                    <i class="fas fa-lightbulb mr-1 text-yellow-500"></i>
                                    <span>Use keyboard shortcuts for faster transactions!</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Payment Modal -->
        <div id="paymentQRModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
            <div class="relative top-10 mx-auto p-6 max-w-6xl bg-white rounded-lg shadow-xl">
                <h3 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-cash-register text-green-600 mr-3"></i>
                    Cash Payment
                </h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Order Summary -->
                    <div>
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shopping-basket text-indigo-600 mr-2"></i>
                            <h4 class="font-semibold text-lg">Order Summary</h4>
                        </div>
                        <div id="orderItems" class="mb-6 max-h-64 overflow-y-auto bg-gray-50 p-4 rounded-lg border">
                            <!-- Order items here -->
                        </div>

                        <div class="border-t pt-4 space-y-3 bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center text-lg">
                                <span class="flex items-center">
                                    <i class="fas fa-receipt text-gray-500 mr-2"></i>
                                    Subtotal
                                </span>
                                <span id="modalSubtotal" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-lg">
                                <span class="flex items-center">
                                    <i class="fas fa-percentage text-gray-500 mr-2"></i>
                                    Tax (12%)
                                </span>
                                <span id="modalTax" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-2xl font-bold text-green-600 border-t pt-3">
                                <span class="flex items-center">
                                    <i class="fas fa-coins mr-2"></i>
                                    Total Amount
                                </span>
                                <span id="modalTotal">₱0.00</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Calculator -->
                    <div>
                        <div class="space-y-6">
                            <!-- Cash Received Input -->
                            <div>
                                <label class="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>
                                    Cash Received
                                </label>
                                <input type="number" id="cashReceived" step="0.01" min="0"
                                       class="w-full px-4 py-4 text-2xl font-bold border-2 rounded-lg focus:ring-2 focus:ring-green-200 focus:border-green-600 transition-all text-center"
                                       placeholder="0.00" oninput="calculateChange()">
                            </div>

                            <!-- Quick Amount Buttons -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Amount</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button onclick="setQuickAmount(100)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium relative">
                                        ₱100
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">1</span>
                                    </button>
                                    <button onclick="setQuickAmount(200)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium relative">
                                        ₱200
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">2</span>
                                    </button>
                                    <button onclick="setQuickAmount(500)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium relative">
                                        ₱500
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">3</span>
                                    </button>
                                    <button onclick="setQuickAmount(1000)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium relative">
                                        ₱1,000
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">4</span>
                                    </button>
                                    <button onclick="setExactAmount()" class="px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded-lg font-medium text-blue-700 relative">
                                        Exact
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">5</span>
                                    </button>
                                    <button onclick="clearCashAmount()" class="px-3 py-2 bg-red-100 hover:bg-red-200 rounded-lg font-medium text-red-700 relative">
                                        Clear
                                        <span class="absolute top-0 right-0 text-xs bg-blue-500 text-white px-1 rounded">6</span>
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 text-center">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Use Ctrl+1-6 for quick amounts
                                </p>
                            </div>

                            <!-- Change Display -->
                            <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-xl font-semibold text-yellow-800 flex items-center">
                                        <i class="fas fa-exchange-alt mr-2"></i>
                                        Change
                                    </span>
                                    <span id="changeAmount" class="text-3xl font-bold text-yellow-800">₱0.00</span>
                                </div>
                                <div id="changeStatus" class="text-sm text-yellow-700 mt-2"></div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-4 mt-8">
                                <button onclick="closePaymentModal()"
                                        class="flex-1 px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors flex items-center justify-center font-semibold">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                    <span class="ml-2 text-xs bg-gray-400 text-white px-2 py-1 rounded">Esc</span>
                                </button>
                                <button id="saveAndPrintBtn" onclick="saveAndPrint()" disabled
                                        class="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    <i class="fas fa-save mr-2"></i>
                                    Save & Print
                                    <span class="ml-2 text-xs bg-green-500 px-2 py-1 rounded">Ctrl+S</span>
                                </button>
                            </div>

                            <!-- Payment Shortcuts Help -->
                            <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="text-xs text-blue-800">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-keyboard mr-2"></i>
                                        <span class="font-semibold">Payment Shortcuts:</span>
                                    </div>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div><kbd class="bg-white px-1 py-0.5 rounded border">Ctrl+E</kbd> Exact Amount</div>
                                        <div><kbd class="bg-white px-1 py-0.5 rounded border">Ctrl+Enter</kbd> Complete</div>
                                        <div><kbd class="bg-white px-1 py-0.5 rounded border">Ctrl+1-6</kbd> Quick Amounts</div>
                                        <div><kbd class="bg-white px-1 py-0.5 rounded border">Esc</kbd> Cancel</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

    <!-- Receipt Template -->
    <div id="receiptTemplate" class="hidden">
    <div style="font-family: 'Courier New', monospace; width: 57mm; margin: 0 auto; padding: 5mm; background-color: #fff;">
        <div style="text-align: left; margin-bottom: 3mm; border-bottom: 1px solid #000; padding-bottom: 2mm;">
            <h2 style="font-size: 12pt; margin-bottom: 2mm; text-align: center;">REFERENCE RECEIPT</h2>
            <p style="font-size: 8pt; margin: 1mm 0;">Date: <span id="receiptDate"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Time: <span id="receiptTime"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Receipt #: <span id="receiptNumber"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Ref #: <span id="receiptReference"></span></p>
        </div>
        <div id="receiptItems" style="margin-bottom: 2mm; border-bottom: 1px dashed #000; padding-bottom: 2mm; font-size: 8pt; text-transform: uppercase;"></div>
        <div style="border-top: 1px solid #000; padding-top: 2mm; margin-top: 2mm;">
            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                <span>Subtotal:</span>
                <span id="receiptSubtotal"></span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                <span>Tax (12%):</span>
                <span id="receiptTax"></span>
            </div>
            <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 2mm; padding-top: 2mm; border-top: 1px solid #000; font-size: 10pt;">
                <span>Total:</span>
                <span id="receiptTotal"></span>
            </div>
        </div>
        <div style="text-align: center; margin-top: 3mm; padding-top: 2mm; border-top: 1px solid #000;">
            <p style="font-size: 8pt;">Thank you for your purchase!</p>

            <!-- Reference Only Disclaimer -->
            <div style="margin-top: 3mm; padding-top: 2mm; border-top: 2px solid #000; font-size: 8pt; text-align: center; background-color: #f0f0f0;">
                <p style="margin: 1mm 0; font-weight: bold; font-size: 9pt;">*** FOR REFERENCE ONLY ***</p>
            </div>

            <!-- BIR Compliance Template (For Future Use) -->
            <div style="margin-top: 2mm; padding-top: 2mm; border-top: 1px dashed #000; font-size: 6pt; text-align: center; color: #888;">
                <p style="margin: 0.5mm 0;">When registered with BIR, update:</p>
                <p style="margin: 0.5mm 0;">TIN: [Your TIN Number]</p>
                <p style="margin: 0.5mm 0;">Permit No: [ATP Number]</p>
                <p style="margin: 0.5mm 0;">Date Issued: [ATP Date]</p>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
    <script src="{% static 'js/sales.js' %}"></script>
    <script>
        // Initialize keyboard shortcuts notification
        document.addEventListener('DOMContentLoaded', function() {
            // Show welcome message for keyboard shortcuts
            setTimeout(function() {
                if (typeof showNotification === 'function') {
                    showNotification('🚀 Keyboard shortcuts enabled! Press Ctrl+H for help or F1-F12 for quick product selection.', 'info');
                }
            }, 2000);

            // Add keyboard shortcut status indicator
            const statusIndicator = document.createElement('div');
            statusIndicator.id = 'keyboardStatus';
            statusIndicator.className = 'fixed bottom-4 right-4 bg-green-100 border border-green-300 text-green-800 px-3 py-2 rounded-lg shadow-lg text-sm z-50';
            statusIndicator.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-keyboard mr-2 text-green-600"></i>
                    <span>Keyboard Shortcuts Active</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-green-600 hover:text-green-800">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(statusIndicator);

            // Auto-hide after 10 seconds
            setTimeout(function() {
                if (statusIndicator.parentNode) {
                    statusIndicator.style.opacity = '0';
                    statusIndicator.style.transition = 'opacity 0.5s';
                    setTimeout(function() {
                        if (statusIndicator.parentNode) {
                            statusIndicator.remove();
                        }
                    }, 500);
                }
            }, 10000);
        });
    </script>
{% endblock %}