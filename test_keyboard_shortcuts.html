<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Keyboard Shortcuts Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <style>
        kbd {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 6px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            color: #495057;
            display: inline-block;
            min-width: 20px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-keyboard mr-3 text-blue-600"></i>
            POS Keyboard Shortcuts Test
        </h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Test Results Panel -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-check-circle mr-2 text-green-600"></i>
                    Test Results
                </h2>
                <div id="testResults" class="space-y-2">
                    <!-- Test results will be populated here -->
                </div>
                <button onclick="runAllTests()" 
                        class="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-play mr-2"></i>
                    Run All Tests
                </button>
            </div>

            <!-- Keyboard Shortcuts Reference -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-list mr-2 text-purple-600"></i>
                    Quick Reference
                </h2>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between items-center">
                        <span>Product Selection:</span>
                        <kbd>F1-F12</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Navigate Products:</span>
                        <kbd>Tab</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Add Product:</span>
                        <kbd>Enter</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Search Products:</span>
                        <kbd>Ctrl+F</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Barcode Scanner:</span>
                        <kbd>Ctrl+B</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Reset Cart:</span>
                        <kbd>Ctrl+R</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Proceed to Checkout:</span>
                        <kbd>F9</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Quick Cash ₱100:</span>
                        <kbd>Ctrl+1</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Exact Amount:</span>
                        <kbd>Ctrl+E</kbd>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Complete Sale:</span>
                        <kbd>Ctrl+S</kbd>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Test Area -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">
                <i class="fas fa-gamepad mr-2 text-orange-600"></i>
                Interactive Test Area
            </h2>
            <p class="text-gray-600 mb-4">
                Press any keyboard shortcut to test it. The system will detect and display the shortcut you pressed.
            </p>
            
            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <div class="text-center">
                    <div id="lastKeyPressed" class="text-2xl font-bold text-blue-600 mb-2">
                        Press a key combination...
                    </div>
                    <div id="keyDescription" class="text-gray-600">
                        Waiting for input
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="text-lg font-semibold text-blue-600" id="fKeyCount">0</div>
                    <div class="text-sm text-gray-600">F-Keys Pressed</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="text-lg font-semibold text-green-600" id="ctrlKeyCount">0</div>
                    <div class="text-sm text-gray-600">Ctrl Combos</div>
                </div>
                <div class="text-center p-3 bg-purple-50 rounded-lg">
                    <div class="text-lg font-semibold text-purple-600" id="navKeyCount">0</div>
                    <div class="text-sm text-gray-600">Navigation Keys</div>
                </div>
                <div class="text-center p-3 bg-orange-50 rounded-lg">
                    <div class="text-lg font-semibold text-orange-600" id="totalKeyCount">0</div>
                    <div class="text-sm text-gray-600">Total Shortcuts</div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>
                How to Use This Test
            </h3>
            <ol class="list-decimal list-inside space-y-2 text-yellow-700">
                <li>Click "Run All Tests" to verify keyboard shortcut functionality</li>
                <li>Use the interactive test area to practice shortcuts</li>
                <li>Press various key combinations to see them detected</li>
                <li>Check the counters to track your usage</li>
                <li>Use this to train staff on keyboard shortcuts</li>
            </ol>
        </div>
    </div>

    <script>
        // Test counters
        let fKeyCount = 0;
        let ctrlKeyCount = 0;
        let navKeyCount = 0;
        let totalKeyCount = 0;

        // Keyboard shortcut descriptions
        const shortcutDescriptions = {
            'F1': 'Quick select product 1',
            'F2': 'Quick select product 2',
            'F3': 'Quick select product 3',
            'F4': 'Quick select product 4',
            'F5': 'Quick select product 5',
            'F6': 'Quick select product 6',
            'F7': 'Quick select product 7',
            'F8': 'Quick select product 8',
            'F9': 'Proceed to checkout',
            'F10': 'Quick select product 10',
            'F11': 'Quick select product 11',
            'F12': 'Quick select product 12',
            'Ctrl+F': 'Focus product search',
            'Ctrl+B': 'Focus barcode scanner',
            'Ctrl+A': 'Add focused product',
            'Ctrl+R': 'Reset cart',
            'Ctrl+P': 'Open payment modal',
            'Ctrl+S': 'Save and print',
            'Ctrl+E': 'Set exact amount',
            'Ctrl+N': 'New transaction',
            'Ctrl+Z': 'Undo last action',
            'Ctrl+H': 'Show help',
            'Ctrl+1': 'Quick cash ₱100',
            'Ctrl+2': 'Quick cash ₱200',
            'Ctrl+3': 'Quick cash ₱500',
            'Ctrl+4': 'Quick cash ₱1000',
            'Ctrl+5': 'Exact amount',
            'Ctrl+6': 'Clear cash amount',
            'Ctrl+Enter': 'Complete transaction',
            'Tab': 'Navigate products forward',
            'Shift+Tab': 'Navigate products backward',
            'Enter': 'Add focused product',
            'Escape': 'Clear focus and search',
            '+': 'Increase quantity',
            '-': 'Decrease quantity',
            'Delete': 'Remove last item',
            'Backspace': 'Remove last item'
        };

        // Listen for keyboard events
        document.addEventListener('keydown', function(e) {
            let keyCombo = '';
            
            if (e.ctrlKey && e.shiftKey) {
                keyCombo = `Ctrl+Shift+${e.code.replace('Key', '')}`;
            } else if (e.ctrlKey) {
                keyCombo = `Ctrl+${e.code.replace('Key', '')}`;
            } else if (e.shiftKey && e.code === 'Tab') {
                keyCombo = 'Shift+Tab';
            } else if (e.code.startsWith('F')) {
                keyCombo = e.code;
            } else {
                switch(e.code) {
                    case 'Tab': keyCombo = 'Tab'; break;
                    case 'Enter': keyCombo = 'Enter'; break;
                    case 'Escape': keyCombo = 'Escape'; break;
                    case 'Equal': keyCombo = '+'; break;
                    case 'Minus': keyCombo = '-'; break;
                    case 'Delete': keyCombo = 'Delete'; break;
                    case 'Backspace': keyCombo = 'Backspace'; break;
                    default: return;
                }
            }

            // Update display
            document.getElementById('lastKeyPressed').textContent = keyCombo;
            document.getElementById('keyDescription').textContent = 
                shortcutDescriptions[keyCombo] || 'Unknown shortcut';

            // Update counters
            if (e.code.startsWith('F')) {
                fKeyCount++;
                document.getElementById('fKeyCount').textContent = fKeyCount;
            } else if (e.ctrlKey) {
                ctrlKeyCount++;
                document.getElementById('ctrlKeyCount').textContent = ctrlKeyCount;
            } else if (['Tab', 'Enter', 'Escape'].includes(keyCombo)) {
                navKeyCount++;
                document.getElementById('navKeyCount').textContent = navKeyCount;
            }

            totalKeyCount++;
            document.getElementById('totalKeyCount').textContent = totalKeyCount;

            // Prevent default for known shortcuts
            if (shortcutDescriptions[keyCombo]) {
                e.preventDefault();
            }
        });

        // Test functions
        function runAllTests() {
            const tests = [
                { name: 'F-Key Detection', test: testFKeys },
                { name: 'Ctrl Combinations', test: testCtrlCombos },
                { name: 'Navigation Keys', test: testNavKeys },
                { name: 'Payment Shortcuts', test: testPaymentShortcuts },
                { name: 'Cart Management', test: testCartManagement }
            ];

            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';

            tests.forEach(({ name, test }) => {
                const result = test();
                const resultDiv = document.createElement('div');
                resultDiv.className = `flex items-center justify-between p-2 rounded ${
                    result.passed ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                }`;
                resultDiv.innerHTML = `
                    <span>${name}</span>
                    <span class="flex items-center">
                        <i class="fas fa-${result.passed ? 'check' : 'times'} mr-1"></i>
                        ${result.passed ? 'PASS' : 'FAIL'}
                    </span>
                `;
                resultsDiv.appendChild(resultDiv);
            });
        }

        function testFKeys() {
            // Test if F-key shortcuts are properly defined
            const fKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
            const definedFKeys = fKeys.filter(key => shortcutDescriptions[key]);
            return { passed: definedFKeys.length === fKeys.length };
        }

        function testCtrlCombos() {
            const ctrlKeys = ['Ctrl+F', 'Ctrl+B', 'Ctrl+A', 'Ctrl+R', 'Ctrl+P', 'Ctrl+S'];
            const definedCtrlKeys = ctrlKeys.filter(key => shortcutDescriptions[key]);
            return { passed: definedCtrlKeys.length === ctrlKeys.length };
        }

        function testNavKeys() {
            const navKeys = ['Tab', 'Enter', 'Escape'];
            const definedNavKeys = navKeys.filter(key => shortcutDescriptions[key]);
            return { passed: definedNavKeys.length === navKeys.length };
        }

        function testPaymentShortcuts() {
            const paymentKeys = ['Ctrl+1', 'Ctrl+2', 'Ctrl+3', 'Ctrl+4', 'Ctrl+E'];
            const definedPaymentKeys = paymentKeys.filter(key => shortcutDescriptions[key]);
            return { passed: definedPaymentKeys.length === paymentKeys.length };
        }

        function testCartManagement() {
            const cartKeys = ['+', '-', 'Delete', 'Ctrl+R', 'Ctrl+Z'];
            const definedCartKeys = cartKeys.filter(key => shortcutDescriptions[key]);
            return { passed: definedCartKeys.length === cartKeys.length };
        }
    </script>
</body>
</html>
